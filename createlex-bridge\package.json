{"name": "createlex-bridge", "version": "1.0.0", "description": "CreateLex Native Bridge App for MCP integration with Unreal Engine", "main": "main.js", "homepage": "https://createlex.com", "repository": {"type": "git", "url": "https://github.com/AlexKissiJr/AiWebplatform.git"}, "bugs": {"url": "https://createlex.com/support", "email": "<EMAIL>"}, "scripts": {"start": "node start.js", "start:prod": "NODE_ENV=production electron .", "start:prod-debug": "set NODE_ENV=production&& set CREATELEX_BASE_URL=https://createlex.com&& set API_BASE_URL=https://createlex.com/api&& set DEV_MODE=false&& set DEBUG_MODE=true&& electron .", "start:prod-debug-mac": "NODE_ENV=production CREATELEX_BASE_URL=https://createlex.com API_BASE_URL=https://createlex.com/api DEV_MODE=false DEBUG_MODE=true electron .", "start:dev": "node start.js --dev", "dev": "set NODE_ENV=development&& set CREATELEX_BASE_URL=http://localhost:3000&& set API_BASE_URL=http://localhost:5001/api&& set DEV_MODE=true&& electron .", "dev:mac": "NODE_ENV=development CREATELEX_BASE_URL=http://localhost:3000 API_BASE_URL=http://localhost:5001/api DEV_MODE=true electron .", "dev:unix": "./start-dev.sh", "build": "npm run build:frontend && npm run build:python && electron-builder", "build:frontend": "node scripts/build-frontend.js", "build:frontend-simple": "node scripts/build-frontend-simple.js", "build:python": "node scripts/copy-python.js", "build:mcp-exe": "node scripts/build-mcp-exe.js", "build:protected": "npm run build:python && npm run build:mcp-exe && npm run build:frontend-simple && electron-builder", "build-win": "npm run build:frontend && npm run build:python && electron-builder --win", "build-win-simple": "npm run build:frontend-simple && npm run build:python && electron-builder --win", "build-win-protected": "npm run build:python && npm run build:mcp-exe && npm run build:frontend-simple && electron-builder --win", "build-win-retry": "node scripts/build-with-retry.js", "build-win-delay": "node scripts/build-with-delay.js", "build-mac": "npm run build:frontend && npm run build:python && electron-builder --mac", "build-mac-simple": "npm run build:frontend-simple && npm run build:python && electron-builder --mac", "build-mac-protected": "npm run build:python && npm run build:mcp-exe && npm run build:frontend-simple && electron-builder --mac", "build-mac-intel-protected": "TARGET_ARCH=x64 npm run build:python && TARGET_ARCH=x64 npm run build:mcp-exe && npm run build:frontend-simple && electron-builder --mac --x64", "build-linux": "npm run build:frontend && npm run build:python && electron-builder --linux", "build-linux-simple": "npm run build:frontend-simple && npm run build:python && electron-builder --linux", "build-linux-protected": "npm run build:python && npm run build:mcp-exe && npm run build:frontend-simple && electron-builder --linux", "deploy": "node scripts/deploy.js", "release": "npm run build:protected && electron-builder --publish=always", "test": "node scripts/test.js", "test-bridge": "node test-native-bridge.js", "create-mcp-update": "node scripts/create-mcp-update.js", "update-check": "node -e \"const {MCPUpdater} = require('./src/updater/mcp-updater'); new MCPUpdater().checkForUpdates().then(console.log).catch(console.error)\"", "test-updater": "node test-mcp-updater.js", "test-cross-platform": "node test-cross-platform.js", "test-macos-mcp": "node test-macos-mcp.js", "test-macos-deps": "node test-macos-dependencies.js", "install-macos-deps": "chmod +x install-macos-deps.sh && ./install-macos-deps.sh", "setup-python-macos": "chmod +x setup-python-macos.sh && ./setup-python-macos.sh", "test-python-detection": "node test-python-detection.js", "install-pyinstaller-macos": "chmod +x install-pyinstaller-macos.sh && ./install-pyinstaller-macos.sh", "test-mcp-direct": "node test-mcp-server-direct.js", "postinstall": "electron-builder install-app-deps", "rebuild": "npm rebuild --runtime=electron --target=28.3.3 --disturl=https://atom.io/download/atom-shell", "dist": "npm run build:protected && electron-builder --publish=never", "dist:all": "npm run build:protected && electron-builder --win --mac --linux --publish=never"}, "author": {"name": "CreateLex", "email": "<EMAIL>", "url": "https://createlex.com"}, "license": "MIT", "build": {"appId": "com.createlex.bridge", "productName": "CreateLex Bridge", "copyright": "Copyright © 2025 CreateLex", "directories": {"output": "dist", "buildResources": "assets"}, "files": ["main.js", "preload.js", "src/**/*", "assets/**/*", "web/**/*", "node_modules/**/*"], "extraResources": [{"from": "src/python", "to": "python"}, {"from": "src/python-protected/", "to": "mcp/"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "artifactName": "CreateLex-Bridge-Setup-${version}.exe", "icon": "assets/icons/logo.png", "publisherName": "CreateLex", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["arm64", "x64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "artifactName": "CreateLex-Bridge-${version}-${arch}.${ext}", "icon": "assets/icons/logo.png", "category": "public.app-category.developer-tools", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist", "identity": "CREATELEX LLC (UWWHD8G966)", "notarize": {"teamId": "UWWHD8G966"}, "extraResources": [{"from": "src/python-protected/mcp_server_mac", "to": "mcp/mcp_server_mac", "filter": ["mcp_server_mac"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "artifactName": "CreateLex-Bridge-${version}-${arch}.${ext}", "icon": "assets/icons/logo.png", "category": "Development", "synopsis": "CreateLex Bridge - MCP integration for Unreal Engine", "extraResources": [{"from": "src/python-protected/mcp_server_linux", "to": "mcp/mcp_server_linux", "filter": ["mcp_server_linux"]}]}, "publish": {"provider": "github", "owner": "AlexKissiJr", "repo": "AiWebplatform"}}, "dependencies": {"electron-store": "^8.1.0", "electron-updater": "^6.1.0", "express": "^4.18.0", "socket.io": "^4.7.0", "axios": "^1.6.0", "python-shell": "^5.0.0", "adm-zip": "^0.5.10", "fs-extra": "^11.2.0"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0", "electron-rebuild": "^3.2.0", "fs-extra": "^11.2.0"}}