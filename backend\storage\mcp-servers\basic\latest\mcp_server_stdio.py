#!/usr/bin/env python3
"""
Simple stdio MCP server for Claude Desktop
"""
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from fastmcp import FastMCP
import json
import socket

# Create FastMCP instance
mcp = FastMCP("UnrealHandshake")

# Import all the tools from the main server
def send_to_unreal(command):
    host = os.environ.get("UNREAL_HOST", "host.docker.internal")
    port = int(os.environ.get("UNREAL_PORT", "9877"))

    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.settimeout(10)
            s.connect((host, port))
        except Exception as e:
            return None

        try:
            json_str = json.dumps(command)
            s.sendall(json_str.encode("utf-8"))
        except Exception as e:
            return None

        buffer_size = 8192
        response_data = b""
        while True:
            try:
                chunk = s.recv(buffer_size)
            except Exception as e:
                return None

            if not chunk:
                break
            response_data += chunk

            try:
                text = response_data.decode("utf-8")
                parsed = json.loads(text)
                return parsed
            except json.JSONDecodeError:
                continue

        if response_data:
            try:
                text = response_data.decode("utf-8")
                parsed = json.loads(text)
                return parsed
            except Exception:
                return None
        else:
            return None

@mcp.tool()
def handshake_test() -> str:
    """Test connection to Unreal Engine with a simple handshake."""
    try:
        response = send_to_unreal({
            "type": "handshake",
            "message": "Hello from MCP Server"
        })
        if response:
            return f"Handshake successful: {response}"
        else:
            return "Handshake failed: No response from Unreal Engine"
    except Exception as e:
        return f"Handshake failed: {str(e)}"

@mcp.tool()
def spawn_actor(actor_class: str, x: float = 0.0, y: float = 0.0, z: float = 0.0) -> str:
    """Spawn an actor in Unreal Engine at the specified location."""
    try:
        response = send_to_unreal({
            "type": "spawn",
            "actor_class": actor_class,
            "location": [x, y, z]
        })
        if response:
            return f"Actor spawned successfully: {response}"
        else:
            return "Failed to spawn actor: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to spawn actor: {str(e)}"

@mcp.tool()
def get_scene_objects() -> str:
    """Get a list of all objects in the current Unreal Engine scene."""
    try:
        response = send_to_unreal({
            "type": "get_all_scene_objects"
        })
        if response:
            return f"Scene objects: {response}"
        else:
            return "Failed to get scene objects: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get scene objects: {str(e)}"

@mcp.tool()
def create_material(material_name: str, base_color: list = [1.0, 1.0, 1.0], metallic: float = 0.0, roughness: float = 0.5) -> str:
    """Create a new material in Unreal Engine."""
    try:
        response = send_to_unreal({
            "type": "create_material",
            "material_name": material_name,
            "base_color": base_color,
            "metallic": metallic,
            "roughness": roughness
        })
        if response:
            return f"Material created successfully: {response}"
        else:
            return "Failed to create material: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to create material: {str(e)}"

@mcp.tool()
def server_status() -> str:
    """Get the current status of the MCP server and its connection to Unreal Engine."""
    try:
        # Test basic connectivity
        response = send_to_unreal({
            "type": "handshake",
            "message": "Status check"
        })
        if response:
            return f"✅ MCP Server Status: Online and connected to Unreal Engine\n🔗 Connection: Active\n📡 Response: {response}"
        else:
            return "⚠️ MCP Server Status: Online but unable to connect to Unreal Engine\n🔗 Connection: Failed\n📡 Please ensure Unreal Engine is running and listening on port 9877"
    except Exception as e:
        return f"❌ MCP Server Status: Error during status check\n🔗 Connection: Error\n📡 Details: {str(e)}"

@mcp.tool()
def get_current_blueprint_context() -> str:
    """Get the current Blueprint context (which Blueprint is currently open/active in the editor)."""
    try:
        response = send_to_unreal({
            "type": "get_current_blueprint_context"
        })
        if response:
            return f"Current Blueprint context: {response}"
        else:
            return "Failed to get Blueprint context: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get Blueprint context: {str(e)}"

@mcp.tool()
def generate_blueprint_function(
    function_name: str, 
    description: str,
    inputs: str = "[]",
    outputs: str = "[]",
    use_current_context: bool = True,
    blueprint_path: str = ""
) -> str:
    """Generate a complete Blueprint function with automatic node creation and wiring.
    
    Args:
        function_name: Name of the function to create
        description: Natural language description of what the function should do
        inputs: JSON string array of input parameters e.g. '[{"name": "Health", "type": "float"}, {"name": "Damage", "type": "float"}]'
        outputs: JSON string array of output parameters e.g. '[{"name": "NewHealth", "type": "float"}]'
        use_current_context: If True, uses the currently open Blueprint. If False, requires blueprint_path
        blueprint_path: Path to the Blueprint to add the function to (only used if use_current_context is False)
    """
    try:
        import json
        inputs_data = json.loads(inputs) if inputs else []
        outputs_data = json.loads(outputs) if outputs else []
        
        response = send_to_unreal({
            "type": "generate_blueprint_function",
            "function_name": function_name,
            "description": description,
            "inputs": inputs_data,
            "outputs": outputs_data,
            "use_current_context": use_current_context,
            "blueprint_path": blueprint_path
        })
        if response:
            return f"Blueprint function generated: {response}"
        else:
            return "Failed to generate Blueprint function: No response from Unreal Engine"
    except json.JSONDecodeError as e:
        return f"Failed to parse JSON inputs/outputs: {str(e)}"
    except Exception as e:
        return f"Failed to generate Blueprint function: {str(e)}"

@mcp.tool()
def analyze_blueprint_graph(
    graph_name: str = "EventGraph",
    use_current_context: bool = True,
    blueprint_path: str = "",
    include_connections: bool = True,
    include_node_details: bool = True
) -> str:
    """Analyze and explain what a Blueprint graph is doing by examining all nodes and connections.
    
    Args:
        graph_name: Name of the graph to analyze (e.g., "EventGraph", "ConstructionScript", or a function name)
        use_current_context: If True, uses the currently open Blueprint. If False, requires blueprint_path
        blueprint_path: Path to the Blueprint to analyze (only used if use_current_context is False)
        include_connections: Include information about how nodes are connected
        include_node_details: Include detailed information about each node's properties
    """
    try:
        response = send_to_unreal({
            "type": "analyze_blueprint_graph",
            "graph_name": graph_name,
            "use_current_context": use_current_context,
            "blueprint_path": blueprint_path,
            "include_connections": include_connections,
            "include_node_details": include_node_details
        })
        if response:
            return f"Blueprint graph analysis: {response}"
        else:
            return "Failed to analyze Blueprint graph: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to analyze Blueprint graph: {str(e)}"

@mcp.tool()
def generate_smart_blueprint_function(
    function_name: str,
    description: str,
    inputs: str = "[]",
    outputs: str = "[]",
    complexity: str = "medium"
) -> str:
    """Generate an intelligent Blueprint function with advanced node creation and wiring.
    
    Args:
        function_name: Name of the function to create
        description: Natural language description of what the function should do
        inputs: JSON string array of input parameters
        outputs: JSON string array of output parameters
        complexity: Function complexity level ("simple", "medium", "complex")
    """
    try:
        import json
        inputs_data = json.loads(inputs) if inputs else []
        outputs_data = json.loads(outputs) if outputs else []
        
        # Analyze the description and generate intelligent node plan
        node_plan = _analyze_function_description(description, inputs_data, outputs_data, complexity)
        
        # Send comprehensive generation request to Unreal
        response = send_to_unreal({
            "type": "generate_smart_blueprint_function",
            "function_name": function_name,
            "description": description,
            "inputs": inputs_data,
            "outputs": outputs_data,
            "node_plan": node_plan
        })
        
        if response and response.get("success"):
            return _format_function_generation_result(response)
        else:
            return f"Failed to generate smart Blueprint function: {response.get('error', 'Unknown error') if response else 'No response'}"
            
    except json.JSONDecodeError as e:
        return f"Failed to parse JSON inputs/outputs: {str(e)}"
    except Exception as e:
        return f"Failed to generate smart Blueprint function: {str(e)}"

@mcp.tool()
def get_blueprint_context_detailed() -> str:
    """Get detailed Blueprint context with full analysis capabilities."""
    try:
        response = send_to_unreal({
            "type": "get_blueprint_context_detailed"
        })
        if response and response.get("success"):
            return _process_blueprint_context(response)
        else:
            return "Failed to get detailed Blueprint context: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get detailed Blueprint context: {str(e)}"

@mcp.tool()
def analyze_blueprint_graph_advanced(
    graph_name: str = "EventGraph",
    analysis_type: str = "comprehensive",
    include_ai_insights: bool = True
) -> str:
    """Perform advanced Blueprint graph analysis with AI-powered insights.
    
    Args:
        graph_name: Name of the graph to analyze
        analysis_type: Type of analysis ("basic", "comprehensive", "performance", "logic_flow")
        include_ai_insights: Whether to include AI-generated insights and suggestions
    """
    try:
        # Get raw graph data from Unreal
        response = send_to_unreal({
            "type": "get_blueprint_graph_data",
            "graph_name": graph_name,
            "include_full_details": True
        })
        
        if not response or not response.get("success"):
            return f"Failed to get graph data: {response.get('error', 'Unknown error')}"
        
        # Process the graph data with advanced analysis
        analysis_result = _perform_advanced_graph_analysis(
            response.get("graph_data", {}),
            analysis_type,
            include_ai_insights
        )
        
        return _format_advanced_analysis_result(analysis_result)
        
    except Exception as e:
        return f"Failed to perform advanced graph analysis: {str(e)}"

# Advanced Blueprint Analysis Functions (Protected Logic in Bridge)
def _analyze_function_description(description, inputs, outputs, complexity):
    """Analyze function description and generate intelligent node plan."""
    node_plan = {
        "estimated_nodes": [],
        "connections": [],
        "layout_hints": {},
        "complexity_level": complexity
    }
    
    desc_lower = description.lower()
    
    # Mathematical operations
    if any(word in desc_lower for word in ["add", "sum", "plus", "+"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_CallFunction",
            "function": "Add_FloatFloat" if "float" in str(inputs) else "Add_IntInt",
            "category": "Math",
            "priority": 1
        })
    
    if any(word in desc_lower for word in ["multiply", "times", "*", "product"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_CallFunction", 
            "function": "Multiply_FloatFloat" if "float" in str(inputs) else "Multiply_IntInt",
            "category": "Math",
            "priority": 1
        })
    
    # Conditional logic
    if any(word in desc_lower for word in ["if", "condition", "check", "branch", "when"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_IfThenElse",
            "category": "Flow Control",
            "priority": 2
        })
    
    # Loops
    if any(word in desc_lower for word in ["loop", "for each", "iterate", "repeat"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_ForEachLoop",
            "category": "Flow Control", 
            "priority": 2
        })
    
    # Variable operations
    if any(word in desc_lower for word in ["get", "read", "retrieve", "access"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_VariableGet",
            "category": "Variable",
            "priority": 3
        })
    
    if any(word in desc_lower for word in ["set", "assign", "store", "save"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_VariableSet", 
            "category": "Variable",
            "priority": 3
        })
    
    # Function calls
    if any(word in desc_lower for word in ["call", "execute", "run", "invoke"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_CallFunction",
            "category": "Function Call",
            "priority": 1
        })
    
    # Sort nodes by priority
    node_plan["estimated_nodes"].sort(key=lambda x: x.get("priority", 5))
    
    # Generate layout hints
    node_plan["layout_hints"] = {
        "flow_direction": "left_to_right",
        "spacing": {"x": 200, "y": 100},
        "group_by_category": True
    }
    
    return node_plan

def _process_blueprint_context(context_data):
    """Process Blueprint context with advanced analysis."""
    try:
        blueprint_info = context_data.get("blueprint_info", {})
        graphs = context_data.get("graphs", [])
        
        # Analyze Blueprint structure
        structure_analysis = {
            "blueprint_type": blueprint_info.get("type", "Unknown"),
            "complexity_score": _calculate_blueprint_complexity(graphs),
            "graph_count": len(graphs),
            "function_count": len([g for g in graphs if g.get("type") == "Function"]),
            "event_count": len([g for g in graphs if g.get("type") == "EventGraph"]),
            "recommendations": _generate_blueprint_recommendations(blueprint_info, graphs)
        }
        
        return json.dumps({
            "success": True,
            "blueprint_info": blueprint_info,
            "structure_analysis": structure_analysis,
            "available_graphs": graphs
        }, indent=2)
        
    except Exception as e:
        return f"Error processing Blueprint context: {str(e)}"

def _calculate_blueprint_complexity(graphs):
    """Calculate Blueprint complexity score."""
    score = 0
    for graph in graphs:
        node_count = graph.get("node_count", 0)
        connection_count = graph.get("connection_count", 0)
        
        # Base complexity from node count
        score += node_count * 1
        
        # Additional complexity from connections
        score += connection_count * 0.5
        
        # Bonus for complex node types
        if graph.get("has_branches", False):
            score += 10
        if graph.get("has_loops", False):
            score += 15
        if graph.get("has_custom_events", False):
            score += 5
    
    return min(score, 100)  # Cap at 100

def _generate_blueprint_recommendations(blueprint_info, graphs):
    """Generate intelligent recommendations for Blueprint improvement."""
    recommendations = []
    
    # Check for common issues
    total_nodes = sum(g.get("node_count", 0) for g in graphs)
    
    if total_nodes > 50:
        recommendations.append({
            "type": "performance",
            "message": "Consider breaking down large graphs into smaller functions for better maintainability",
            "priority": "medium"
        })
    
    function_graphs = [g for g in graphs if g.get("type") == "Function"]
    if len(function_graphs) == 0:
        recommendations.append({
            "type": "organization", 
            "message": "Consider creating custom functions to organize your Blueprint logic",
            "priority": "low"
        })
    
    # Check for missing documentation
    undocumented_functions = [g for g in function_graphs if not g.get("has_documentation", False)]
    if len(undocumented_functions) > 0:
        recommendations.append({
            "type": "documentation",
            "message": f"{len(undocumented_functions)} functions could benefit from documentation",
            "priority": "low"
        })
    
    return recommendations

def _perform_advanced_graph_analysis(graph_data, analysis_type, include_ai_insights):
    """Perform comprehensive graph analysis."""
    import time
    analysis = {
        "graph_name": graph_data.get("name", "Unknown"),
        "analysis_type": analysis_type,
        "timestamp": time.time()
    }
    
    nodes = graph_data.get("nodes", [])
    connections = graph_data.get("connections", [])
    
    if analysis_type in ["basic", "comprehensive"]:
        analysis["node_analysis"] = _analyze_nodes(nodes)
        analysis["connection_analysis"] = _analyze_connections(connections)
    
    if analysis_type in ["comprehensive", "logic_flow"]:
        analysis["execution_flow"] = _analyze_execution_flow(nodes, connections)
        analysis["data_flow"] = _analyze_data_flow(nodes, connections)
    
    if analysis_type in ["comprehensive", "performance"]:
        analysis["performance_metrics"] = _analyze_performance(nodes, connections)
    
    if include_ai_insights:
        analysis["ai_insights"] = _generate_ai_insights(graph_data, analysis)
    
    return analysis

def _analyze_nodes(nodes):
    """Analyze node distribution and types."""
    node_types = {}
    for node in nodes:
        node_type = node.get("type", "Unknown")
        node_types[node_type] = node_types.get(node_type, 0) + 1
    
    return {
        "total_nodes": len(nodes),
        "node_types": node_types,
        "most_common": max(node_types.items(), key=lambda x: x[1]) if node_types else ("None", 0)
    }

def _analyze_connections(connections):
    """Analyze connection patterns."""
    exec_connections = [c for c in connections if c.get("type") == "execution"]
    data_connections = [c for c in connections if c.get("type") == "data"]
    
    return {
        "total_connections": len(connections),
        "execution_connections": len(exec_connections),
        "data_connections": len(data_connections),
        "average_connections_per_node": len(connections) / max(len(set(c.get("from_node") for c in connections)), 1)
    }

def _analyze_execution_flow(nodes, connections):
    """Analyze execution flow patterns."""
    exec_connections = [c for c in connections if c.get("type") == "execution"]
    
    # Find entry points (nodes with no incoming execution)
    incoming_exec = set(c.get("to_node") for c in exec_connections)
    all_nodes = set(range(len(nodes)))
    entry_points = all_nodes - incoming_exec
    
    return {
        "entry_points": len(entry_points),
        "execution_paths": len(exec_connections),
        "has_branches": any(node.get("type") == "Branch" for node in nodes),
        "has_loops": any(node.get("type") in ["ForEachLoop", "WhileLoop"] for node in nodes)
    }

def _analyze_data_flow(nodes, connections):
    """Analyze data flow patterns."""
    data_connections = [c for c in connections if c.get("type") == "data"]
    
    return {
        "data_dependencies": len(data_connections),
        "variable_reads": len([n for n in nodes if n.get("type") == "GetVariable"]),
        "variable_writes": len([n for n in nodes if n.get("type") == "SetVariable"])
    }

def _analyze_performance(nodes, connections):
    """Analyze potential performance issues."""
    performance_score = 100
    issues = []
    
    # Check for expensive operations
    expensive_nodes = [n for n in nodes if n.get("type") in ["ForEachLoop", "WhileLoop"]]
    if len(expensive_nodes) > 3:
        performance_score -= 20
        issues.append("Multiple loops detected - consider optimization")
    
    # Check for excessive branching
    branch_nodes = [n for n in nodes if n.get("type") == "Branch"]
    if len(branch_nodes) > 5:
        performance_score -= 15
        issues.append("High branching complexity")
    
    return {
        "performance_score": max(performance_score, 0),
        "potential_issues": issues,
        "optimization_suggestions": _generate_optimization_suggestions(nodes, connections)
    }

def _generate_optimization_suggestions(nodes, connections):
    """Generate performance optimization suggestions."""
    suggestions = []
    
    # Check for redundant variable access
    var_gets = [n for n in nodes if n.get("type") == "GetVariable"]
    var_names = [n.get("variable_name") for n in var_gets]
    duplicates = set([name for name in var_names if var_names.count(name) > 1])
    
    if duplicates:
        suggestions.append("Consider caching frequently accessed variables")
    
    # Check for long execution chains
    exec_connections = [c for c in connections if c.get("type") == "execution"]
    if len(exec_connections) > 10:
        suggestions.append("Consider breaking long execution chains into functions")
    
    return suggestions

def _generate_ai_insights(graph_data, analysis):
    """Generate AI-powered insights about the Blueprint."""
    insights = []
    
    # Pattern recognition
    node_count = analysis.get("node_analysis", {}).get("total_nodes", 0)
    
    if node_count > 20:
        insights.append({
            "type": "complexity",
            "message": "This graph shows high complexity. Consider refactoring into smaller, focused functions.",
            "confidence": 0.8
        })
    
    # Logic pattern insights
    if analysis.get("execution_flow", {}).get("has_branches", False):
        insights.append({
            "type": "logic_pattern",
            "message": "Detected conditional logic patterns. Ensure all code paths are tested.",
            "confidence": 0.9
        })
    
    return insights

def _format_function_generation_result(response):
    """Format the function generation result for user display."""
    result = response.get("result", {})
    
    return f"""✅ Blueprint Function Generated Successfully!

Function: {result.get('function_name', 'Unknown')}
Blueprint: {result.get('blueprint_path', 'Unknown')}
Nodes Created: {result.get('nodes_created', 0)}
Connections Made: {result.get('connections_made', 0)}

Summary: {result.get('summary', 'Function created with basic implementation')}

{json.dumps(result, indent=2)}"""

def _format_advanced_analysis_result(analysis):
    """Format advanced analysis results for user display."""
    return f"""🔍 Advanced Blueprint Analysis Results

Graph: {analysis.get('graph_name', 'Unknown')}
Analysis Type: {analysis.get('analysis_type', 'Unknown')}

📊 Node Analysis:
- Total Nodes: {analysis.get('node_analysis', {}).get('total_nodes', 0)}
- Node Types: {len(analysis.get('node_analysis', {}).get('node_types', {}))}

🔗 Connection Analysis:
- Total Connections: {analysis.get('connection_analysis', {}).get('total_connections', 0)}
- Execution Flow: {analysis.get('connection_analysis', {}).get('execution_connections', 0)}
- Data Flow: {analysis.get('connection_analysis', {}).get('data_connections', 0)}

⚡ Performance Score: {analysis.get('performance_metrics', {}).get('performance_score', 'N/A')}

🤖 AI Insights: {len(analysis.get('ai_insights', []))} insights generated

{json.dumps(analysis, indent=2)}"""

if __name__ == "__main__":
    # Run as stdio MCP server
    mcp.run() 